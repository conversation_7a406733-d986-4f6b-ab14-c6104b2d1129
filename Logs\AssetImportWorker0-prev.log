Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-10T06:22:04Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Projects/AnimationGenerator
-logFile
Logs/AssetImportWorker0.log
-srvPort
13392
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Projects/AnimationGenerator
D:/Unity/Projects/AnimationGenerator
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [11280]  Target information:

Player connection [11280]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 3857275348 [EditorId] 3857275348 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [11280]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3857275348 [EditorId] 3857275348 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [11280] Host joined multi-casting on [***********:54997]...
Player connection [11280] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2630.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Projects/AnimationGenerator/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:          Intel
    VRAM:            6107 MB
    App VRAM Budget: 5546 MB
    Driver:          31.0.101.2130
    Unified Memory Architecture
    Cache Coherent UMA
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56528
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.016173 seconds.
- Loaded All Assemblies, in 66.438 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1431 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.720 seconds
Domain Reload Profiling: 70159ms
	BeginReloadAssembly (9346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (12815ms)
	RebuildNativeTypeToScriptingClass (69ms)
	initialDomainReloadingComplete (36069ms)
	LoadAllAssembliesAndSetupDomain (8135ms)
		LoadAssemblies (9379ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (8081ms)
			TypeCache.Refresh (8072ms)
				TypeCache.ScanAssembly (6554ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (3724ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3555ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2147ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (255ms)
			ProcessInitializeOnLoadAttributes (703ms)
			ProcessInitializeOnLoadMethodAttributes (431ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 24.917 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 13.51 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 23.693 seconds
Domain Reload Profiling: 48605ms
	BeginReloadAssembly (920ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (163ms)
	RebuildNativeTypeToScriptingClass (55ms)
	initialDomainReloadingComplete (152ms)
	LoadAllAssembliesAndSetupDomain (23620ms)
		LoadAssemblies (19568ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (4721ms)
			TypeCache.Refresh (4126ms)
				TypeCache.ScanAssembly (3833ms)
			BuildScriptInfoCaches (529ms)
			ResolveRequiredComponents (57ms)
	FinalizeReload (23695ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (18240ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (322ms)
			SetLoadedEditorAssemblies (171ms)
			BeforeProcessingInitializeOnLoad (2841ms)
			ProcessInitializeOnLoadAttributes (13387ms)
			ProcessInitializeOnLoadMethodAttributes (1510ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.24 seconds
Refreshing native plugins compatible for Editor in 3.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7810 unused Assets / (6.1 MB). Loaded Objects now: 8556.
Memory consumption went from 193.7 MB to 187.6 MB.
Total: 31.925400 ms (FindLiveObjects: 2.571500 ms CreateObjectMapping: 2.266800 ms MarkObjects: 19.953400 ms  DeleteObjects: 7.131900 ms)

========================================================================
Received Import Request.
  Time since last request: 407887.570308 seconds.
  path: Assets/Scripts/Game/Managers/PostProcessingManager.cs
  artifactKey: Guid(e485490115e5e8d4c891b3d2291d0357) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Game/Managers/PostProcessingManager.cs using Guid(e485490115e5e8d4c891b3d2291d0357) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e5b8c8aac525e10a2e0534a28f22074c') in 0.0359501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 158.224856 seconds.
  path: Assets/Scripts
  artifactKey: Guid(babda9553fd5c934eb8d6331d93dfb7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts using Guid(babda9553fd5c934eb8d6331d93dfb7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a984a9bda3dce52cc1c0655b4dad4c7') in 0.0011655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.474738 seconds.
  path: Assets/Scripts/Game/GUI
  artifactKey: Guid(7ecc2fcf6b84b694fa565cd3aedcdbd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Game/GUI using Guid(7ecc2fcf6b84b694fa565cd3aedcdbd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4d44185f9e6685e11e80234b60522fad') in 0.0058604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

