using UnityEngine;

namespace Game.Data
{
    /// <summary>
    /// Configuration settings for the animation generator system.
    /// </summary>
    public class GeneratorConfig
    {
        /// <summary>
        /// Configuration settings for Gemini AI integration.
        /// </summary>
        public GeminiConfig GeminiConfig;

        /// <summary>
        /// Configuration settings for OpenAI Text-to-Speech integration.
        /// </summary>
        public OpenAITextToSpeechConfig OpenAITextToSpeechConfig;

        /// <summary>
        /// Asset addresses for the generator.
        /// </summary>
        public string AssetAddresses;

        /// <summary>
        /// Scenario description for the generator.
        /// </summary>
        public string Scenario;
    }

    /// <summary>
    /// Configuration settings for Gemini AI API integration.
    /// </summary>
    public class GeminiConfig
    {
        /// <summary>
        /// API key for Gemini service authentication.
        /// </summary>
        public string ApiKey;

        /// <summary>
        /// Model name to use for Gemini API calls.
        /// </summary>
        public string Model = "gemini-2.5-flash";

        /// <summary>
        /// System instruction to provide context to the AI model.
        /// </summary>
        public string SystemInstruction = "You are a helpful AI assistant.";

        /// <summary>
        /// Temperature setting for response randomness (0.0 to 2.0).
        /// </summary>
        public float Temperature = 1.0f;

        /// <summary>
        /// Top-p sampling parameter for nucleus sampling.
        /// </summary>
        public float TopP = 0.95f;

        /// <summary>
        /// Top-k sampling parameter for limiting token selection.
        /// </summary>
        public int TopK = 40;

        /// <summary>
        /// Thinking budget for the model (-1 for unlimited).
        /// </summary>
        public int ThinkingBudget = -1;
    }

    /// <summary>
    /// Configuration settings for OpenAI Text-to-Speech API integration.
    /// </summary>
    public class OpenAITextToSpeechConfig
    {
        /// <summary>
        /// API key for OpenAI service authentication.
        /// </summary>
        public string ApiKey;

        /// <summary>
        /// API URL endpoint for text-to-speech requests.
        /// </summary>
        public string ApiUrl = "https://api.openai.com/v1/audio/speech";

        /// <summary>
        /// Model name to use for text-to-speech generation.
        /// </summary>
        public string Model = "tts-1";
    }
}