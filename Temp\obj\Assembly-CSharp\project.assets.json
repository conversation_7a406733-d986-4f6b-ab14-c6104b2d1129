{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Game/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SmartVertex.Tools": "1.0.0"}, "compile": {"bin/placeholder/Game.dll": {}}, "runtime": {"bin/placeholder/Game.dll": {}}}, "RTLTMPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/RTLTMPro.dll": {}}, "runtime": {"bin/placeholder/RTLTMPro.dll": {}}}, "RTLTMPro-Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"RTLTMPro": "1.0.0"}, "compile": {"bin/placeholder/RTLTMPro-Editor.dll": {}}, "runtime": {"bin/placeholder/RTLTMPro-Editor.dll": {}}}, "SmartVertex.EditorTools/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SmartVertex.Tools": "1.0.0"}, "compile": {"bin/placeholder/SmartVertex.EditorTools.dll": {}}, "runtime": {"bin/placeholder/SmartVertex.EditorTools.dll": {}}}, "SmartVertex.Tools/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/SmartVertex.Tools.dll": {}}, "runtime": {"bin/placeholder/SmartVertex.Tools.dll": {}}}}}, "libraries": {"Game/1.0.0": {"type": "project", "path": "Game.csproj", "msbuildProject": "Game.csproj"}, "RTLTMPro/1.0.0": {"type": "project", "path": "RTLTMPro.csproj", "msbuildProject": "RTLTMPro.csproj"}, "RTLTMPro-Editor/1.0.0": {"type": "project", "path": "RTLTMPro-Editor.csproj", "msbuildProject": "RTLTMPro-Editor.csproj"}, "SmartVertex.EditorTools/1.0.0": {"type": "project", "path": "SmartVertex.EditorTools.csproj", "msbuildProject": "SmartVertex.EditorTools.csproj"}, "SmartVertex.Tools/1.0.0": {"type": "project", "path": "SmartVertex.Tools.csproj", "msbuildProject": "SmartVertex.Tools.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Game >= 1.0.0", "RTLTMPro >= 1.0.0", "RTLTMPro-Editor >= 1.0.0", "SmartVertex.EditorTools >= 1.0.0", "SmartVertex.Tools >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity\\Projects\\AnimationGenerator\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity\\Projects\\AnimationGenerator\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity\\Projects\\AnimationGenerator\\Game.csproj": {"projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\Game.csproj"}, "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro-Editor.csproj": {"projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro-Editor.csproj"}, "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj": {"projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj"}, "D:\\Unity\\Projects\\AnimationGenerator\\SmartVertex.EditorTools.csproj": {"projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\SmartVertex.EditorTools.csproj"}, "D:\\Unity\\Projects\\AnimationGenerator\\SmartVertex.Tools.csproj": {"projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\SmartVertex.Tools.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413\\RuntimeIdentifierGraph.json"}}}}